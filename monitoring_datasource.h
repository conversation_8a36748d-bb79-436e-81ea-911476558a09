#ifndef MONITORING_DATASOURCE_H
#define MONITORING_DATASOURCE_H

#include <QObject>
#include <QTimer>
#include <QVariantList>
#include <QDateTime>
#include <QThread>
#include <QMutex>
#include <QThreadPool>
#include <QVector>
#include <QStringList>
#include <QVariantMap>
#include <QDebug>
#include "csvreader.h"  // 添加CSV读取器
#include "redisclient.h"  // 添加Redis客户端

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

// Qt 5.x forward declaration
namespace QtCharts {
    class QAbstractSeries;
}

class MonitoringDataSource : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QVariantList smokeO2Data READ smokeO2Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeCOData READ smokeCOData NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeSwitch1Data READ smokeSwitch1Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeTableData READ smokeTableData NOTIFY smokeTableDataChanged)
    Q_PROPERTY(QStringList boilerList READ boilerList NOTIFY boilerListChanged)
    Q_PROPERTY(QString currentBoiler READ currentBoiler WRITE setCurrentBoiler NOTIFY currentBoilerChanged)
    Q_PROPERTY(bool isRunning READ isRunning WRITE setIsRunning NOTIFY isRunningChanged)
    Q_PROPERTY(bool isDataConnected READ isDataConnected NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString currentTemperature READ currentTemperature NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentVoltage READ currentVoltage NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentCurrent READ currentCurrent NOTIFY currentDataChanged)
    Q_PROPERTY(bool isRedisConnected READ isRedisConnected NOTIFY redisConnectionChanged)

public:
    explicit MonitoringDataSource(QObject *parent = nullptr);

    // 属性访问器 - 从CSV文件动态读取数据
    QVariantList smokeO2Data() const;
    QVariantList smokeCOData() const;
    QVariantList smokeSwitch1Data() const;
    QVariantList smokeTableData() const { return m_smokeTableData; }
    QStringList boilerList() const { return m_boilerList; }
    QString currentBoiler() const { return m_currentBoiler; }
    bool isRunning() const { return m_isRunning; }
    bool isDataConnected() const { return m_isDataConnected; }
    QString connectionStatus() const { return m_connectionStatus; }
    QString currentTemperature() const { return m_currentTemperature; }
    QString currentVoltage() const { return m_currentVoltage; }
    QString currentCurrent() const { return m_currentCurrent; }

    // 获取当前设备的采集间隔（秒）
    Q_INVOKABLE int getCurrentCollectionInterval() const;

    void setIsRunning(bool running);
    void setCurrentBoiler(const QString &boiler);

public slots:
    void startMonitoring();
    void stopMonitoring();
    void clearData();
    void updateSmokeChartSeries(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex = 0);
    void updateSmokeChartSeriesWithMinutes(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries);
    void updateSmokeChartSeriesWithScroll(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex, double scrollOffset);
    void updateChartIncremental(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex);





signals:
    void smokeDataChanged();
    void smokeTableDataChanged();
    void boilerListChanged();
    void currentBoilerChanged();
    void isRunningChanged();
    void chartDataUpdated();
    void dataConnectionChanged();
    void currentDataChanged();
    void redisConnectionChanged();
    // boilerSwitchCompleted信号已移除，使用简化的切换逻辑

private slots:
    void updateData();

private:
    void updateSmokeData();
    void loadBoilerList();
    void reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler);
    void addSmokeTableRow(double o2, double co, double temperature, double voltage, double current, int switch1);
    void updateTimerInterval();

    // CSV数据读取相关方法
    QVariantList readO2DataFromCsv() const;
    QVariantList readCODataFromCsv() const;
    QVariantList readSwitch1DataFromCsv() const;

    // 图表更新方法 - 基于CSV数据
    void updateChartFromCsv(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex = 0);

    QTimer *m_timer;

    // CSV读取器
    CsvReader *m_csvReader;

    // 表格数据
    QVariantList m_smokeTableData;

    // 表格更新计数器 - 每三次采集更新一次表格
    int m_tableUpdateCounter;

    // 烟气分析仪相关
    QStringList m_boilerList;  // 保持变量名以兼容现有代码
    QString m_currentBoiler;   // 保持变量名以兼容现有代码

    // 反吹反馈控制相关
    bool m_isBackflowActive;           // 反吹反馈是否激活
    bool m_isDataUpdateSuspended;      // 氧气和一氧化碳数据更新是否暂停
    QTimer *m_backflowDelayTimer;      // 反吹反馈延迟恢复定时器
    int m_backflowDelayTime;           // 延迟时间（秒）
    QString m_suspendedO2Value;        // 暂停时保存的氧气浓度值
    QString m_suspendedCOValue;        // 暂停时保存的一氧化碳浓度值

    // 反吹反馈控制方法
    void checkBackflowStatus(int switch1);
    void suspendO2COUpdates();
    void resumeO2COUpdates();
    int getBackflowDelayTime() const;

    bool m_isRunning;
    bool m_isDataConnected;
    QString m_connectionStatus;
    int m_dataCount;
    
    // 当前数据值
    QString m_currentTemperature;
    QString m_currentVoltage;
    QString m_currentCurrent;

    // 相对时间轴相关变量
    QDateTime m_dataStartTime;  // 数据采集开始时间
    bool m_dataStartTimeSet;    // 是否已设置开始时间

    // CSV数据读取常量（简化版本）
    static const int DEFAULT_RECENT_POINTS = 1000;      // 默认读取的最近数据点数（CSV读取无内存压力）

    static const int MAX_TABLE_ROWS = 5;
    static const int TABLE_UPDATE_INTERVAL = 4;  // 每4次采集更新一次表格和CSV缓存

    // Redis缓存相关
    RedisClient *m_redisClient;
    bool m_redisEnabled;
    QString m_redisHost;
    int m_redisPort;
    QString m_redisPassword;
    int m_redisDatabase;
    int m_redisCacheExpireHours;



    // Redis缓存方法
    void initializeRedis();
    void loadRedisConfig();
    QString generateCacheKey(int zoomIndex) const;
    void preGenerateAllTimeRangeCaches();
    bool setCachedChartData(const QString &key, const QVariantList &data);
    QVariantList getCachedChartData(const QString &key);
    bool hasCachedChartData(const QString &key);
    bool isRedisConnected() const;
};

#endif // MONITORING_DATASOURCE_H
